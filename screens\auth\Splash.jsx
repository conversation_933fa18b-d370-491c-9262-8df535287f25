import React, { useEffect, useState } from "react";
import { View, StyleSheet, Image, Animated as RNAnimated } from "react-native";
import Animated, {
  useSharedValue,
  withTiming,
  useAnimatedStyle,
  Easing,
  withSpring,
  withSequence,
  runOnJS,
} from "react-native-reanimated";
import { AppColors } from "../../utils/AppColors";
import { FULL_HEIGHT } from "../../utils/Constants";
import { AppImages } from "../../utils/AppImages";
import { getToken, setAddress, setUser } from "../../redux/slices/userSlice";
import { useDispatch } from "react-redux";
import { getMe } from "../../api/Auth";
import * as Location from "expo-location";

const Splash = (props) => {
  const dispatch = useDispatch();
  // Shared values for animation
  const positionY = useSharedValue(-FULL_HEIGHT / 2); // Start above the screen
  const size = useSharedValue(71); // Initial size
  const [showLogo, setShowLogo] = useState(false);

  const handlePermission = async () => {
    const { status } = await Location.getForegroundPermissionsAsync();
    if (status === "granted") {
      return true;
    } else if (status === "undetermined") {
      const { status } = await Location.requestForegroundPermissionsAsync();
      return status === "granted";
    }
    return false;
  };

  const getCoordinates = async () => {
    const hasPermission = await handlePermission();
    if (hasPermission) {
      const { coords } = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = coords;
      const address = await getAddress(latitude, longitude);
      return {
        latitude,
        longitude,
        address,
      };
    } else {
      const { latitude, longitude } = {
        latitude: 8.4996555,
        longitude: 76.9242809,
      };
      const address = await getAddress(latitude, longitude);
      return {
        latitude,
        longitude,
        address,
      };
    }
  };

  const getAddress = async (latitude, longitude) => {
    try {
      const response = await Location.reverseGeocodeAsync({
        latitude,
        longitude,
      });
      if (response.length > 0) {
        const { city, street } = response[0];
        return `${street}, ${city}`;
      }
      return null;
    } catch (error) {
      console.error("Error getting address:", error);
      return null;
    }
  };

  useEffect(() => {
    const animationSequence = () => {
      positionY.value = withSpring(0, { damping: 10, stiffness: 100 });
      size.value = withSequence(
        withTiming(111, {
          duration: 1000,
          easing: Easing.bezier(0.5, 0.01, 0, 1),
        }),
        withTiming(183, {
          duration: 1000,
          easing: Easing.bezier(0.5, 0.01, 0, 1),
        }),
        withTiming(FULL_HEIGHT + 200, {
          duration: 500,
          easing: Easing.bezier(0.5, 0.01, 0, 1),
        }),
        withTiming(
          FULL_HEIGHT + 200,
          {
            duration: 1000, // Delay for logo to appear
            easing: Easing.bezier(0.5, 0.01, 0, 1),
          },
          () => {
            runOnJS(setShowLogo)(true);
          }
        )
      );
    };

    animationSequence();
  }, []);

  // Animated style for the circle
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateY: positionY.value }],
      width: size.value,
      height: size.value,
      borderRadius: size.value / 2, // To keep it circular
    };
  });

  // Fade in animation for the logo
  const logoOpacity = new RNAnimated.Value(0);

  useEffect(() => {
    checkTokenandNavigate();
  }, [showLogo]);

  const checkTokenandNavigate = async () => {
    const token = await getToken();

    if (showLogo) {
      RNAnimated.timing(logoOpacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start(() => {
        setTimeout(async () => {
          const address = await getCoordinates();
          if (address) {
            dispatch(setAddress(address));
          }
          if (token) {
            try {
              const user = await getMe();

              dispatch(setUser(user));
              props.navigation.navigate("TabNavigator", { screen: "Discover" });
            } catch (error) {
              console.error(error);
              props.navigation.navigate("OnBoarding");
            }
          } else {
            props.navigation.navigate("OnBoarding");
          }
        }, 3000);
      });
    }
  };

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.circle, animatedStyle]} />
      {showLogo && (
        <RNAnimated.View
          style={[styles.logoContainer, { opacity: logoOpacity }]}
        >
          <Image source={AppImages.SPLASH_LOGO} style={styles.logo} />
          {/* <LottieView
            autoPlay
            style={{
              width: 100,
              height: 40,
            }}
            source={AppImages.LOTTIE}
          /> */}
        </RNAnimated.View>
      )}
    </View>
  );
};

export default Splash;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: AppColors.whiteColor,
  },
  circle: {
    backgroundColor: AppColors.primaryColor,
  },
  logoContainer: {
    position: "absolute",
    justifyContent: "center",
    alignItems: "center",
  },
  logo: {
    width: 208,
    height: 88,
  },
});
