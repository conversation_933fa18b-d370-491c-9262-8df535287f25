import { View, Text, StyleSheet } from "react-native";
import React from "react";
import {
  openTermsAndConditions,
  openPrivacyPolicy,
  openContentPolicites,
} from "../../../utils/WebLinks";
import { AppColors } from "../../../utils/AppColors";

const Terms = () => {
  return (
    <View style={styles.container}>
      <Text style={styles.text}>By continuing, you agree to our .</Text>
      <View style={{ flexDirection: "row", columnGap: 5 }}>
        <Text onPress={openTermsAndConditions} style={styles.underlineText}>
          Terms of Service
        </Text>{" "}
        <Text onPress={openPrivacyPolicy} style={styles.underlineText}>
          Privacy Policy
        </Text>
        <Text onPress={openContentPolicites} style={styles.underlineText}>
          Content Policies
        </Text>
      </View>
    </View>
  );
};

export default Terms;

const styles = StyleSheet.create({
  container: {
    width: "80%",
    alignItems: "center",
    flexDirection: "column",
    marginTop: 10,
  },
  text: {
    fontFamily: "Poppins_500Medium",
    fontSize: 14,
    textAlign: "center",
    color: AppColors.textColor,
  },
  underlineText: {
    fontSize: 12,
    fontFamily: "Poppins_400Regular",
    color: AppColors.textColor,
    textDecorationLine: "underline",
  },
});
