import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
} from "react-native";
import React, { useEffect, useState } from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { OtpInput } from "react-native-otp-entry";
import { useRoute } from "@react-navigation/native";
import GlobalStyles from "../../styles/GlobalStyles";
import { AppColors } from "../../utils/AppColors";
import CommonButton from "../../components/common/buttons/CommonButton";
import ErrorText from "../../components/common/texts/ErrorText";
import Checkbox from "expo-checkbox";
import messaging from "@react-native-firebase/messaging";
import {
  openPrivacyPolicy,
  openTermsAndConditions,
} from "../../utils/WebLinks";
import Header from "../../components/sections/verfiyNumber/Header";
import { RegisterFCMToken, VerifyLoginApi } from "../../api/Auth";
import { storeToken } from "../../redux/slices/userSlice";

const PhoneVerification = (props) => {
  const route = useRoute();
  const phone = route.params.phone;

  const [otp, setOtp] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [isChecked, setIsChecked] = useState(false);
  const [fcm, setFcm] = useState("");

  const requestUserPermission = async () => {
    const authStatus = await messaging().requestPermission();
    const enabled =
      authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
      authStatus === messaging.AuthorizationStatus.PROVISIONAL;

    if (enabled) {
    }
  };

  useEffect(() => {
    if (requestUserPermission()) {
      messaging()
        .getToken()
        .then((token) => {
          setFcm(token);
          console.log("FCM Token:", token);
        });
    } else {
      return;
    }
  }, []);

  const handleOtp = async () => {
    setLoading(true);

    if (isChecked === false) {
      setError("Please agree to the terms and conditions");
      setLoading(false);
      return;
    }

    if (otp.length < 6) {
      setError("Invalid OTP");
      setLoading(false);
      return;
    }

    try {
      const res = await VerifyLoginApi({ phone_number: phone, otp: otp });

      storeToken(res.access_token);
      await RegisterFCMToken(fcm);
      setLoading(false);
      setError("");

      props.navigation.navigate("TabNavigator", { screen: "Discover" });
    } catch (error) {
      console.error(error);

      setError("Invalid OTP");
      setLoading(false);
      return;
    }
  };

  const resendOtp = async () => {};

  return (
    <SafeAreaView style={[styles.container, GlobalStyles.androidSafeArea]}>
      {/* header */}
      <Header navigation={props.navigation} />

      <View style={{ padding: 20 }}>
        <Text style={styles.heading}>Verify phone number</Text>
        <Text style={styles.text}>
          Enter the 4-digit code sent to you at{" "}
          <Text style={{ color: AppColors.primaryColor }}>{phone}</Text>
        </Text>
        <OtpInput
          numberOfDigits={6}
          focusColor={AppColors.primaryColor}
          focusStickBlinkingDuration={500}
          onFilled={(val) => {
            setOtp(val);
          }}
          theme={{
            pinCodeContainerStyle: styles.otpContainerStyle,
            pinCodeTextStyle: styles.pinCodeText,
            filledPinCodeContainerStyle: {
              borderColor: AppColors.primaryColor,
            },
          }}
        />
        <View style={styles.checkBoxContainer}>
          <Checkbox
            style={styles.checkbox}
            value={isChecked}
            onValueChange={setIsChecked}
            color={isChecked ? AppColors.primaryColor : AppColors.blackColor}
          />
          <View style={{ flexDirection: "row", columnGap: 2 }}>
            <Text
              style={{
                color: AppColors.secondaryText,
                fontFamily: "Poppins_400Regular",
                fontSize: 12,
              }}
            >
              I agree to the
            </Text>
            <TouchableOpacity onPress={openTermsAndConditions}>
              <Text
                style={{
                  color: AppColors.blackColor,
                  fontFamily: "Poppins_400Regular",
                  fontSize: 12,
                }}
              >
                Terms And Conditions
              </Text>
            </TouchableOpacity>

            <Text
              style={{
                color: AppColors.secondaryText,
                fontFamily: "Poppins_400Regular",
                fontSize: 12,
              }}
            >
              {" "}
              and{" "}
            </Text>
            <TouchableOpacity onPress={openPrivacyPolicy}>
              <Text
                style={{
                  color: AppColors.blackColor,
                  fontFamily: "Poppins_400Regular",
                  fontSize: 12,
                }}
              >
                Privacy Policy
              </Text>
            </TouchableOpacity>
          </View>
        </View>
        <CommonButton
          disable={loading}
          buttonStyle={{
            marginVertical: 18,
          }}
          title={
            loading ? (
              <ActivityIndicator size="small" color={AppColors.whiteColor} />
            ) : (
              "Confirm"
            )
          }
          pressHandler={handleOtp}
        />

        <View style={{ justifyContent: "center", flexDirection: "row" }}>
          <ErrorText errorText={error} />
        </View>

        <View style={styles.resendWrapper}>
          <Text
            style={{
              color: AppColors.secondaryText,
              fontFamily: "Poppins_400Regular",
              fontSize: 12,
              marginVertical: 10,
              textAlign: "center",
            }}
          >
            Didn’t receive code?
          </Text>
          <Text
            onPress={() => {
              resendOtp();
            }}
            style={{
              color: AppColors.primaryColor,
              fontFamily: "Poppins_600SemiBold",
              fontSize: 12,
              textAlign: "center",
            }}
          >
            RESEND CODE
          </Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default PhoneVerification;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: AppColors.whiteColor,
    paddingTop: 10,
  },
  heading: {
    color: AppColors.blackColor,
    fontFamily: "Poppins_600Bold",
    fontSize: 24,
    textAlign: "center",
  },
  text: {
    color: AppColors.secondaryText,
    fontFamily: "Poppins_400Regular",
    fontSize: 14,
    marginVertical: 10,
    textAlign: "center",
    marginBottom: 40,
  },

  resendWrapper: {
    flexDirection: "row",
    alignItems: "center",
    columnGap: 12,
    justifyContent: "center",
  },

  checkBoxContainer: {
    flexDirection: "row",
    alignItems: "center",
    columnGap: 8,
  },
  checkbox: {
    borderRadius: 5,
  },
  otpContainerStyle: {
    width: "15%",
    borderColor: "rgba(0, 0, 0, 0.05)",
    marginVertical: 10,
    backgroundColor: "rgba(250, 250, 250, 1)",
  },
  pinCodeText: { color: AppColors.blackColor },
});
