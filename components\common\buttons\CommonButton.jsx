import { StyleSheet, Text, TouchableOpacity } from "react-native";

import { useFonts, Poppins_600SemiBold } from "@expo-google-fonts/poppins";
import { AppColors } from "../../../utils/AppColors";

const CommonButton = ({ disable, pressHandler, buttonStyle, title }) => {
  useFonts({
    Poppins_600SemiBold,
  });
  const styles = StyleSheet.create({
    container: {
      alignItems: "center",
      justifyContent: "center",
      flexDirection: "row",
      padding: 15,
      paddingHorizontal: 25,
      borderRadius: 220,
      width: "100%",
      backgroundColor: AppColors.primaryColor,
      opacity: disable ? 0.8 : 1,
    },
    text: {
      color: AppColors.whiteColor,
      fontSize: 16,
      fontFamily: "Poppins_600SemiBold",
    },
  });
  return (
    <TouchableOpacity
      disabled={disable}
      style={[styles.container, buttonStyle]}
      onPress={pressHandler}
    >
      <Text style={styles.text}>{title}</Text>
    </TouchableOpacity>
  );
};

export default CommonButton;
