import AsyncStorage from "@react-native-async-storage/async-storage";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";

const USER_ADDRESS_KEY = "userAddress"; // Key for storing address

const initialAddressState = { latitude: null, longitude: null, address: null };

const initialState = {
  user: null,
  token: null,
  address: initialAddressState,
};

export const storeToken = async (token) => {
  try {
    await AsyncStorage.setItem("token", token);
  } catch (err) {
    console.error("Error storing token:", err);
  }
};
// Retrieve the token from local storage
export const getToken = async () => {
  try {
    const token = await AsyncStorage.getItem("token");

    console.log("token");

    if (token !== null) {
      return token;
    } else {
      return null;
    }
  } catch (err) {
    console.error("Error getting token:", err);
    return null;
  }
};

// Remove the token from local storage
export const removeToken = () => {
  try {
    // localStorage.removeItem('token');
    AsyncStorage.removeItem("token");
  } catch (err) {
    console.error("Error removing token:", err);
  }
};

// --- Address Helpers ---
export const storeAddressInStorage = async (addressObject) => {
  try {
    // Only store if addressObject is provided and has content, otherwise remove
    if (
      addressObject &&
      (addressObject.latitude ||
        addressObject.longitude ||
        addressObject.address)
    ) {
      await AsyncStorage.setItem(
        USER_ADDRESS_KEY,
        JSON.stringify(addressObject)
      );
    } else {
      // If addressObject is null, undefined, or an empty shell, remove it from storage
      await AsyncStorage.removeItem(USER_ADDRESS_KEY);
    }
  } catch (err) {
    console.error("Error storing address:", err);
  }
};

export const getAddressFromStorage = async () => {
  try {
    const addressString = await AsyncStorage.getItem(USER_ADDRESS_KEY);
    if (addressString) {
      return JSON.parse(addressString);
    }
    return null; // Return null if not found, thunk/reducer will handle setting default
  } catch (err) {
    console.error("Error getting address from storage:", err);
    return null; // Return null on error
  }
};

export const removeAddressFromStorage = async () => {
  try {
    await AsyncStorage.removeItem(USER_ADDRESS_KEY);
  } catch (err) {
    console.error("Error removing address from storage:", err);
  }
};

export const initializeAuth = createAsyncThunk(
  "user/initializeAuth",
  async (_, { dispatch }) => {
    const token = await getTokenFromStorage();
    const address = await getAddressFromStorage(); // This might be null or an address object
    return { token, address /*, user */ };
  }
);

export const UserSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
    },
    setAddress: (state, action) => {
      state.address = action.payload
        ? { ...initialAddressState, ...action.payload }
        : { ...initialAddressState };
      storeAddressInStorage(action.payload);
    },
     logoutUser: (state) => {
      state.user = null;
      state.token = null;
      state.address = { ...initialAddressState }; // Reset address to initial object structure
      removeTokenFromStorage();
      removeAddressFromStorage();
    },
  },
});

export const { setUser, setAddress, logoutUser } = UserSlice.actions;

export default UserSlice.reducer;
